{"snapshots": {"/home/<USER>/repo/sd-customer-success/src/main/resources/application.yml": {"filePath": "/home/<USER>/repo/sd-customer-success/src/main/resources/application.yml", "baseContent": "spring:\n  application:\n    name: customer-success-analytics\n  \n  datasource:\n    url: *******************************************    username: postgres\n    password: password\n    hikari:\n      maximum-pool-size: 10\n      minimum-idle: 5\n      idle-timeout: 30000\n      pool-name: SpringBootJPAHikariCP\n      max-lifetime: 2000000\n      connection-timeout: 30000\n\n  jpa:\n    hibernate:\n      ddl-auto: create-drop\n    show-sql: true\n    properties:\n      hibernate:\n        dialect: org.hibernate.dialect.H2Dialect\n        format_sql: true\n        use_sql_comments: true\n        hbm2ddl:\n          auto: create-drop\n        globally_quoted_identifiers: false\n        jdbc:\n          batch_size: 20\n        order_inserts: true\n        order_updates: true\n    open-in-view: false\n\n  h2:\n    console:\n      enabled: true\n\n  thymeleaf:\n    cache: false\n    prefix: classpath:/templates/\n    suffix: .html\n    encoding: UTF-8\n    mode: HTML\n\n  security:\n    user:\n      name: <EMAIL>\n      password: test@123\n\n  servlet:\n    multipart:\n      max-file-size: 10MB\n      max-request-size: 10MB\n\n  cache:\n    type: simple\n    cache-names:\n      - dashboardMetrics\n      - dauTrend\n      - tenantTrend\n      - engagementMetrics\n      - topUsers\n      - topTenants\n      - appUsage\n      - retentionMetrics\n      - aggregatedMetrics\n      - tenantUsageTrends\n\nserver:\n  port: 8080\n  servlet:\n    context-path: /\n    session:\n      timeout: 30m\n\nlogging:\n  level:\n    com.kylas: DEBUG\n    org.springframework.security: DEBUG\n    org.hibernate.SQL: DEBUG\n    org.hibernate.type.descriptor.sql.BasicBinder: TRACE\n  pattern:\n    console: \"%d{yyyy-MM-dd HH:mm:ss} - %msg%n\"\n    file: \"%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n\"\n\n# Application specific properties\napp:\n  csv:\n    upload-dir: ${java.io.tmpdir}/csv-uploads\n    batch-size: 1000\n    max-file-size: 10MB\n    allowed-extensions: csv\n  analytics:\n    cache-duration: 3600 # seconds\n    default-date-range: 30 # days\n  security:\n    session-timeout: 1800 # seconds\n  data:\n    archive:\n      retention-days: 90\n      batch-size: 1000\n      history-retention-days: 365\n      enabled: true\n      schedule: \"0 0 2 * * ?\" # Daily at 2 AM\n  external:\n    api:\n      base-url: http://localhost:8080\n      timeout: 30s\n      retry:\n        max-attempts: 3\n        delay: 1s\n\n# Management and monitoring\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics,prometheus\n  endpoint:\n    health:\n      show-details: always\n  metrics:\n    export:\n      prometheus:\n        enabled: true\n", "baseTimestamp": 1757907761351}}}